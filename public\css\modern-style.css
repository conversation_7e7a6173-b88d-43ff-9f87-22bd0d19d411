/* Modern Chat Application Styles */

/* CSS Variables for consistent theming */
:root {
  --primary-color: #438ac7;
  --secondary-color: #337AB7;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-color: #dee2e6;
  --text-color: #212529;
  --bg-color: #ffffff;
  --shadow: 0 2px 4px rgba(0,0,0,0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  direction: rtl;
  overflow-x: hidden;
}

/* Container and layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.chat-container {
  max-width: 400px;
  margin: 0 auto;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  height: 100vh;
  max-height: 600px;
}

/* Header styles */
.chat-header {
  background: var(--primary-color);
  color: white;
  padding: 15px;
  text-align: center;
  position: relative;
}

.chat-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.connection-status {
  font-size: 12px;
  margin-top: 5px;
  opacity: 0.9;
}

.connection-status.success {
  color: #90EE90;
}

.connection-status.danger {
  color: #FFB6C1;
}

/* Login forms */
.login-container {
  padding: 20px;
  background: var(--bg-color);
}

.login-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 2px solid var(--border-color);
}

.login-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.login-tab.active {
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.login-form {
  display: none;
}

.login-form.active {
  display: block;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-color);
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
  direction: rtl;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 138, 199, 0.1);
}

.form-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.form-checkbox input {
  margin-left: 8px;
}

/* Buttons */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #3a7bc8;
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-full {
  width: 100%;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
}

/* Chat interface */
.chat-interface {
  display: none;
  height: 100%;
  flex-direction: column;
}

.chat-interface.active {
  display: flex;
}

/* Navigation tabs */
.chat-nav {
  display: flex;
  background: var(--light-color);
  border-bottom: 1px solid var(--border-color);
}

.nav-item {
  flex: 1;
  padding: 10px 5px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  font-size: 12px;
  position: relative;
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
}

.nav-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: var(--danger-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Messages area */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background: #f8f9fa;
}

.message {
  margin-bottom: 10px;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  background: white;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  animation: fadeIn 0.3s ease;
}

.message.own {
  background: var(--primary-color);
  color: white;
  margin-right: 20px;
}

.message.system {
  background: var(--info-color);
  color: white;
  text-align: center;
  font-style: italic;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
}

.message-username {
  font-weight: 600;
}

.message-time {
  opacity: 0.7;
}

.message-content {
  word-wrap: break-word;
}

/* Input area */
.input-container {
  padding: 15px;
  background: white;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  padding: 10px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  resize: none;
  max-height: 100px;
  min-height: 40px;
  direction: rtl;
}

.message-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.send-btn {
  padding: 10px 15px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.send-btn:hover {
  background: #3a7bc8;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Online users */
.users-list {
  max-height: 300px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
}

.user-item:hover {
  background: var(--light-color);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-left: 10px;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.user-status {
  font-size: 12px;
  color: #666;
}

/* Utility classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .container {
    padding: 10px;
  }
  
  .chat-container {
    max-width: 100%;
    height: 100vh;
    border-radius: 0;
  }
  
  .form-input {
    padding: 10px;
  }
  
  .btn {
    padding: 10px 16px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #2d3748;
    --text-color: #e2e8f0;
    --border-color: #4a5568;
    --light-color: #4a5568;
  }
  
  body {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }
}
