require('dotenv').config();
const express = require('express');
const http = require('http');
const path = require('path');
const compression = require('compression');
const morgan = require('morgan');

// Import configurations
const connectDB = require('./config/database');
const { createRateLimiter, corsOptions, helmetConfig, helmet, cors } = require('./config/security');

// Import socket manager
const SocketManager = require('./socket/socketManager');

// Import routes
const uploadRoutes = require('./routes/upload');
const apiRoutes = require('./routes/api');

// Import models to ensure they're registered
require('./models/User');
require('./models/Room');
require('./models/Message');
require('./models/Log');

class ChatServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.port = process.env.PORT || 3000;
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketIO();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet(helmetConfig));
    this.app.use(cors(corsOptions));

    // Compression and logging
    this.app.use(compression());
    this.app.use(morgan('combined'));

    // Rate limiting
    this.app.use('/api/', createRateLimiter(15 * 60 * 1000, 100)); // 100 requests per 15 minutes
    this.app.use('/upload/', createRateLimiter(5 * 60 * 1000, 10)); // 10 uploads per 5 minutes

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files
    this.app.use(express.static('public'));
    this.app.use('/uploads', express.static('uploads'));

    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', true);
  }

  setupRoutes() {
    // API routes
    this.app.use('/api', apiRoutes);
    this.app.use('/upload', uploadRoutes);

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        socketStats: this.socketManager ? this.socketManager.getStats() : null
      });
    });

    // Serve main application
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });

    // Control panel (if exists)
    this.app.get('/cp', (req, res) => {
      const cpPath = path.join(__dirname, 'public', 'cp.html');
      if (require('fs').existsSync(cpPath)) {
        res.sendFile(cpPath);
      } else {
        res.status(404).send('Control panel not found');
      }
    });

    // Catch-all route for SPA
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });
  }

  setupSocketIO() {
    this.socketManager = new SocketManager(this.server);
    console.log('Socket.IO server initialized');
  }

  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'الصفحة غير موجودة',
        message: 'الرابط المطلوب غير متوفر'
      });
    });

    // Global error handler
    this.app.use((err, req, res, next) => {
      console.error('Server error:', err);

      // Don't leak error details in production
      const isDevelopment = process.env.NODE_ENV === 'development';
      
      res.status(err.status || 500).json({
        error: 'خطأ في الخادم',
        message: isDevelopment ? err.message : 'حدث خطأ غير متوقع',
        ...(isDevelopment && { stack: err.stack })
      });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      // Don't exit the process in production
      if (process.env.NODE_ENV === 'development') {
        process.exit(1);
      }
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      // Graceful shutdown
      this.shutdown();
    });

    // Graceful shutdown on SIGTERM
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      this.shutdown();
    });

    // Graceful shutdown on SIGINT (Ctrl+C)
    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      this.shutdown();
    });
  }

  async start() {
    try {
      // Connect to database
      await connectDB();
      console.log('Database connected successfully');

      // Initialize default data
      await this.initializeDefaultData();

      // Start server
      this.server.listen(this.port, () => {
        console.log(`🚀 Chat server running on port ${this.port}`);
        console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🌐 Access: http://localhost:${this.port}`);
      });

    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  async initializeDefaultData() {
    try {
      const Room = require('./models/Room');
      
      // Create default room if it doesn't exist
      const defaultRoomId = process.env.DEFAULT_ROOM_ID || 'efOiAhhNdL';
      const defaultRoomName = process.env.DEFAULT_ROOM_NAME || 'الغرفة العامة 1';
      
      const existingRoom = await Room.findOne({ id: defaultRoomId });
      
      if (!existingRoom) {
        await Room.create({
          id: defaultRoomId,
          topic: defaultRoomName,
          about: 'الغرفة الرئيسية للدردشة العامة',
          owner: 'system',
          needpass: false,
          max: 0,
          welcome: `مرحباً بكم في ${defaultRoomName}`,
          settings: {
            allowGuests: true,
            allowFiles: true,
            allowImages: true,
            allowAudio: true,
            maxMessageLength: 250
          }
        });
        
        console.log(`✅ Default room "${defaultRoomName}" created`);
      }

    } catch (error) {
      console.error('Failed to initialize default data:', error);
    }
  }

  shutdown() {
    console.log('Shutting down server...');
    
    this.server.close(() => {
      console.log('HTTP server closed');
      
      // Close database connection
      const mongoose = require('mongoose');
      mongoose.connection.close(() => {
        console.log('Database connection closed');
        process.exit(0);
      });
    });

    // Force close after 10 seconds
    setTimeout(() => {
      console.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  }
}

// Start the server
if (require.main === module) {
  const server = new ChatServer();
  server.start();
}

module.exports = ChatServer;
