const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const DeviceFingerprint = require('../utils/deviceFingerprint');

// Import models
const User = require('../models/User');
const Room = require('../models/Room');
const Message = require('../models/Message');
const Log = require('../models/Log');

const router = express.Router();

// Middleware to validate request
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'بيانات غير صحيحة',
      errors: errors.array()
    });
  }
  next();
};

// Middleware to authenticate JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'رمز المصادقة مطلوب'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: 'رمز مصادقة غير صحيح'
    });
  }
};

// User registration
router.post('/register', [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('اسم المستخدم يجب أن يكون بين 3 و 50 حرف')
    .matches(/^[a-zA-Z0-9\u0600-\u06FF\s_-]+$/)
    .withMessage('اسم المستخدم يحتوي على أحرف غير مسموحة'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  
  body('fingerprint')
    .notEmpty()
    .withMessage('بصمة الجهاز مطلوبة')
], validateRequest, async (req, res) => {
  try {
    const { username, password, fingerprint, clientData } = req.body;

    // Check if username already exists
    const existingUser = await User.findOne({ topic: username });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم موجود بالفعل'
      });
    }

    // Generate enhanced fingerprint
    const fingerprintGen = new DeviceFingerprint();
    const enhancedFingerprint = fingerprintGen.generateFromRequest(req, clientData);

    // Create new user
    const user = new User({
      topic: username,
      topic1: username,
      password,
      fp: enhancedFingerprint,
      ip: req.ip,
      loginG: false,
      stat: 1,
      token: jwt.sign({ username }, process.env.JWT_SECRET),
      roomid: process.env.DEFAULT_ROOM_ID || 'efOiAhhNdL'
    });

    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, username: user.topic },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // Log registration
    await Log.logUserAction(user._id, 'register', {
      username: user.topic,
      ip: req.ip,
      fingerprint: enhancedFingerprint,
      userAgent: req.get('User-Agent')
    });

    res.status(201).json({
      success: true,
      message: 'تم التسجيل بنجاح',
      user: {
        id: user._id,
        topic: user.topic,
        topic1: user.topic1,
        ucol: user.ucol,
        mcol: user.mcol,
        bg: user.bg,
        pic: user.pic,
        rep: user.rep,
        msg: user.msg
      },
      token
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في التسجيل'
    });
  }
});

// User login
router.post('/login', [
  body('username').trim().notEmpty().withMessage('اسم المستخدم مطلوب'),
  body('password').notEmpty().withMessage('كلمة المرور مطلوبة')
], validateRequest, async (req, res) => {
  try {
    const { username, password, stealth = false } = req.body;

    // Find user
    const user = await User.findOne({ topic: username });
    if (!user || !(await user.comparePassword(password))) {
      // Log failed attempt
      await Log.createSecurityLog({
        state: 'login_failed',
        topic: username,
        ip: req.ip,
        fingerprint: req.body.fingerprint || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: 'medium'
      });

      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيح'
      });
    }

    // Update user status
    user.stat = stealth ? 0 : 1;
    user.lastssen = new Date();
    user.ip = req.ip;
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, username: user.topic },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // Log successful login
    await Log.logUserAction(user._id, 'login_success', {
      username: user.topic,
      ip: req.ip,
      fingerprint: req.body.fingerprint || 'unknown',
      userAgent: req.get('User-Agent'),
      stealth
    });

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user._id,
        topic: user.topic,
        topic1: user.topic1,
        ucol: user.ucol,
        mcol: user.mcol,
        bg: user.bg,
        pic: user.pic,
        ico: user.ico,
        rep: user.rep,
        msg: user.msg,
        roomid: user.roomid
      },
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تسجيل الدخول'
    });
  }
});

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = req.user;
    
    res.json({
      success: true,
      user: {
        id: user._id,
        topic: user.topic,
        topic1: user.topic1,
        ucol: user.ucol,
        mcol: user.mcol,
        bg: user.bg,
        pic: user.pic,
        ico: user.ico,
        rep: user.rep,
        msg: user.msg,
        roomid: user.roomid,
        lastssen: user.lastssen,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الملف الشخصي'
    });
  }
});

// Update user profile
router.put('/profile', authenticateToken, [
  body('topic1').optional().trim().isLength({ max: 100 }),
  body('msg').optional().trim().isLength({ max: 100 }),
  body('ucol').optional().matches(/^#[0-9A-F]{6}$/i),
  body('mcol').optional().matches(/^#[0-9A-F]{6}$/i),
  body('bg').optional().matches(/^#[0-9A-F]{6}$/i)
], validateRequest, async (req, res) => {
  try {
    const user = req.user;
    const { topic1, msg, ucol, mcol, bg } = req.body;

    // Update allowed fields
    if (topic1 !== undefined) user.topic1 = topic1;
    if (msg !== undefined) user.msg = msg;
    if (ucol !== undefined) user.ucol = ucol;
    if (mcol !== undefined) user.mcol = mcol;
    if (bg !== undefined) user.bg = bg;

    await user.save();

    res.json({
      success: true,
      message: 'تم تحديث الملف الشخصي بنجاح',
      user: {
        id: user._id,
        topic: user.topic,
        topic1: user.topic1,
        ucol: user.ucol,
        mcol: user.mcol,
        bg: user.bg,
        pic: user.pic,
        ico: user.ico,
        rep: user.rep,
        msg: user.msg
      }
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث الملف الشخصي'
    });
  }
});

// Get rooms list
router.get('/rooms', async (req, res) => {
  try {
    const rooms = await Room.getPublicRooms();
    
    res.json({
      success: true,
      rooms: rooms.map(room => ({
        id: room.id,
        topic: room.topic,
        about: room.about,
        pic: room.pic,
        totalUsers: room.totalUsers,
        max: room.max,
        needpass: room.needpass
      }))
    });
  } catch (error) {
    console.error('Rooms list error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب قائمة الغرف'
    });
  }
});

// Get online users count
router.get('/online', async (req, res) => {
  try {
    const onlineCount = await User.countDocuments({ stat: 1 });
    
    res.json({
      success: true,
      count: onlineCount
    });
  } catch (error) {
    console.error('Online count error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب عدد المتصلين'
    });
  }
});

// Generate device fingerprint
router.post('/fingerprint', async (req, res) => {
  try {
    const { clientData } = req.body;
    
    const fingerprintGen = new DeviceFingerprint();
    const fingerprint = fingerprintGen.generateFromRequest(req, clientData);
    
    res.json({
      success: true,
      fingerprint
    });
  } catch (error) {
    console.error('Fingerprint generation error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في توليد بصمة الجهاز'
    });
  }
});

module.exports = router;
