const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const DeviceFingerprint = require('../utils/deviceFingerprint');
const User = require('../models/User');
const Room = require('../models/Room');
const Message = require('../models/Message');
const Log = require('../models/Log');

class SocketManager {
  constructor(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.SOCKET_CORS_ORIGIN || "*",
        methods: process.env.SOCKET_CORS_METHODS?.split(',') || ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.connectedUsers = new Map(); // socketId -> userInfo
    this.userSockets = new Map(); // userId -> Set of socketIds
    this.roomUsers = new Map(); // roomId -> Set of userIds
    
    this.setupMiddleware();
    this.setupEventHandlers();
  }

  setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        const fingerprint = socket.handshake.auth.fingerprint;
        
        if (!fingerprint) {
          return next(new Error('Device fingerprint required'));
        }

        // Validate fingerprint format
        if (!DeviceFingerprint.isValid(fingerprint)) {
          return next(new Error('Invalid device fingerprint'));
        }

        socket.fingerprint = fingerprint;
        socket.ip = socket.handshake.address;
        
        // If token provided, verify it
        if (token) {
          try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const user = await User.findById(decoded.userId);
            if (user) {
              socket.userId = user._id.toString();
              socket.user = user;
            }
          } catch (err) {
            console.log('Invalid token:', err.message);
          }
        }

        next();
      } catch (error) {
        console.error('Socket middleware error:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Rate limiting middleware
    this.io.use((socket, next) => {
      const now = Date.now();
      if (!socket.lastMessage) {
        socket.lastMessage = now;
        socket.messageCount = 1;
      } else {
        const timeDiff = now - socket.lastMessage;
        if (timeDiff < 1000) { // Less than 1 second
          socket.messageCount++;
          if (socket.messageCount > 5) {
            return next(new Error('Rate limit exceeded'));
          }
        } else {
          socket.messageCount = 1;
        }
        socket.lastMessage = now;
      }
      next();
    });
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`New connection: ${socket.id}`);
      
      // Store connection info
      this.connectedUsers.set(socket.id, {
        socketId: socket.id,
        userId: socket.userId,
        fingerprint: socket.fingerprint,
        ip: socket.ip,
        connectedAt: new Date(),
        lastActivity: new Date()
      });

      // Setup event handlers
      this.setupUserEvents(socket);
      this.setupRoomEvents(socket);
      this.setupMessageEvents(socket);
      this.setupDisconnectEvents(socket);
    });
  }

  setupUserEvents(socket) {
    // Guest login
    socket.on('guest_login', async (data) => {
      try {
        await this.handleGuestLogin(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في تسجيل دخول الزائر' });
      }
    });

    // User login
    socket.on('user_login', async (data) => {
      try {
        await this.handleUserLogin(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في تسجيل الدخول' });
      }
    });

    // User registration
    socket.on('user_register', async (data) => {
      try {
        await this.handleUserRegistration(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في التسجيل' });
      }
    });

    // Logout
    socket.on('logout', () => {
      this.handleLogout(socket);
    });

    // Update user status
    socket.on('update_status', async (data) => {
      try {
        await this.handleStatusUpdate(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في تحديث الحالة' });
      }
    });
  }

  setupRoomEvents(socket) {
    // Join room
    socket.on('join_room', async (data) => {
      try {
        await this.handleJoinRoom(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في دخول الغرفة' });
      }
    });

    // Leave room
    socket.on('leave_room', async (data) => {
      try {
        await this.handleLeaveRoom(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في مغادرة الغرفة' });
      }
    });

    // Get room list
    socket.on('get_rooms', async () => {
      try {
        const rooms = await Room.getPublicRooms();
        socket.emit('rooms_list', rooms);
      } catch (error) {
        socket.emit('error', { message: 'فشل في جلب قائمة الغرف' });
      }
    });

    // Get online users
    socket.on('get_online_users', async (data) => {
      try {
        const users = await this.getOnlineUsers(data.roomId);
        socket.emit('online_users', users);
      } catch (error) {
        socket.emit('error', { message: 'فشل في جلب المستخدمين المتصلين' });
      }
    });
  }

  setupMessageEvents(socket) {
    // Send message
    socket.on('send_message', async (data) => {
      try {
        await this.handleSendMessage(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في إرسال الرسالة' });
      }
    });

    // Like message
    socket.on('like_message', async (data) => {
      try {
        await this.handleLikeMessage(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في الإعجاب بالرسالة' });
      }
    });

    // Delete message
    socket.on('delete_message', async (data) => {
      try {
        await this.handleDeleteMessage(socket, data);
      } catch (error) {
        socket.emit('error', { message: 'فشل في حذف الرسالة' });
      }
    });

    // Get message history
    socket.on('get_messages', async (data) => {
      try {
        const messages = await Message.getRecentMessages(data.roomId, data.limit || 50);
        socket.emit('message_history', messages);
      } catch (error) {
        socket.emit('error', { message: 'فشل في جلب تاريخ الرسائل' });
      }
    });
  }

  setupDisconnectEvents(socket) {
    socket.on('disconnect', () => {
      this.handleDisconnect(socket);
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
      this.logSecurityEvent(socket, 'socket_error', { error: error.message });
    });
  }

  async handleGuestLogin(socket, data) {
    const { username, clientData } = data;
    
    if (!username || username.trim().length === 0) {
      throw new Error('اسم المستخدم مطلوب');
    }

    // Generate enhanced fingerprint
    const fingerprintGen = new DeviceFingerprint();
    const fingerprint = fingerprintGen.generateFromRequest(
      { ip: socket.ip, get: () => null }, 
      clientData
    );

    // Create guest user session
    const guestUser = {
      id: socket.id,
      topic: username.trim(),
      topic1: username.trim(),
      loginG: true,
      stat: 1,
      ip: socket.ip,
      fp: fingerprint,
      roomid: process.env.DEFAULT_ROOM_ID || 'efOiAhhNdL',
      ucol: '#000000',
      mcol: '#000000',
      bg: '#FFFFFF',
      pic: 'pic.png',
      rep: 0,
      msg: 'زائر',
      connectedAt: new Date()
    };

    socket.user = guestUser;
    socket.userId = socket.id;

    // Join default room
    await this.joinRoom(socket, guestUser.roomid);

    // Log the login
    await Log.createSecurityLog({
      state: 'login_success',
      topic: username,
      ip: socket.ip,
      fingerprint,
      userAgent: socket.handshake.headers['user-agent'],
      details: { type: 'guest', clientData }
    });

    socket.emit('login_success', {
      user: guestUser,
      token: null
    });
  }

  async handleUserLogin(socket, data) {
    const { username, password, stealth = false } = data;

    if (!username || !password) {
      throw new Error('اسم المستخدم وكلمة المرور مطلوبان');
    }

    const user = await User.findOne({ topic: username.trim() });
    
    if (!user || !(await user.comparePassword(password))) {
      // Log failed attempt
      await Log.createSecurityLog({
        state: 'login_failed',
        topic: username,
        ip: socket.ip,
        fingerprint: socket.fingerprint,
        userAgent: socket.handshake.headers['user-agent'],
        severity: 'medium'
      });
      
      throw new Error('اسم المستخدم أو كلمة المرور غير صحيح');
    }

    // Update user status
    user.stat = stealth ? 0 : 1;
    user.lastssen = new Date();
    user.ip = socket.ip;
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, username: user.topic },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    socket.user = user;
    socket.userId = user._id.toString();

    // Join user's room
    await this.joinRoom(socket, user.roomid);

    // Log successful login
    await Log.logUserAction(user._id, 'login_success', {
      username: user.topic,
      ip: socket.ip,
      fingerprint: socket.fingerprint,
      userAgent: socket.handshake.headers['user-agent'],
      stealth
    });

    socket.emit('login_success', {
      user: user.toObject(),
      token
    });
  }

  async joinRoom(socket, roomId) {
    // Leave current room if any
    if (socket.currentRoom) {
      socket.leave(socket.currentRoom);
      this.removeUserFromRoom(socket.currentRoom, socket.userId);
    }

    // Join new room
    socket.join(roomId);
    socket.currentRoom = roomId;
    
    // Add user to room tracking
    if (!this.roomUsers.has(roomId)) {
      this.roomUsers.set(roomId, new Set());
    }
    this.roomUsers.get(roomId).add(socket.userId);

    // Update user socket mapping
    if (!this.userSockets.has(socket.userId)) {
      this.userSockets.set(socket.userId, new Set());
    }
    this.userSockets.get(socket.userId).add(socket.id);

    // Broadcast user joined
    if (socket.user && !socket.user.loginG) {
      const joinMessage = {
        type: 'join',
        username: socket.user.displayName || socket.user.topic,
        userId: socket.userId,
        roomId,
        timestamp: new Date(),
        bg: 'none',
        class: 'hmsg'
      };

      this.io.to(roomId).emit('user_joined', joinMessage);
    }

    // Send room info and online users
    const onlineUsers = await this.getOnlineUsers(roomId);
    socket.emit('room_joined', {
      roomId,
      onlineUsers,
      userCount: onlineUsers.length
    });
  }

  async getOnlineUsers(roomId) {
    const userIds = this.roomUsers.get(roomId) || new Set();
    const users = [];

    for (const userId of userIds) {
      const connectionInfo = Array.from(this.connectedUsers.values())
        .find(conn => conn.userId === userId);
      
      if (connectionInfo && connectionInfo.userId) {
        if (connectionInfo.userId.length === 24) { // MongoDB ObjectId
          const user = await User.findById(connectionInfo.userId);
          if (user && user.stat === 1) {
            users.push({
              id: user._id,
              topic: user.topic,
              topic1: user.topic1,
              pic: user.pic,
              ico: user.ico,
              ucol: user.ucol,
              mcol: user.mcol,
              rep: user.rep,
              msg: user.msg
            });
          }
        } else {
          // Guest user
          const guestInfo = Array.from(this.connectedUsers.values())
            .find(conn => conn.socketId === connectionInfo.userId);
          if (guestInfo) {
            users.push({
              id: guestInfo.socketId,
              topic: guestInfo.username || 'زائر',
              pic: 'pic.png',
              ucol: '#000000',
              mcol: '#000000',
              rep: 0,
              msg: 'زائر'
            });
          }
        }
      }
    }

    return users;
  }

  removeUserFromRoom(roomId, userId) {
    if (this.roomUsers.has(roomId)) {
      this.roomUsers.get(roomId).delete(userId);
      if (this.roomUsers.get(roomId).size === 0) {
        this.roomUsers.delete(roomId);
      }
    }
  }

  handleDisconnect(socket) {
    console.log(`User disconnected: ${socket.id}`);

    // Remove from room
    if (socket.currentRoom) {
      this.removeUserFromRoom(socket.currentRoom, socket.userId);
      
      // Broadcast user left
      if (socket.user && !socket.user.loginG) {
        this.io.to(socket.currentRoom).emit('user_left', {
          userId: socket.userId,
          username: socket.user.displayName || socket.user.topic
        });
      }
    }

    // Clean up tracking
    this.connectedUsers.delete(socket.id);
    
    if (this.userSockets.has(socket.userId)) {
      this.userSockets.get(socket.userId).delete(socket.id);
      if (this.userSockets.get(socket.userId).size === 0) {
        this.userSockets.delete(socket.userId);
      }
    }

    // Update user status if registered user
    if (socket.user && !socket.user.loginG) {
      User.findByIdAndUpdate(socket.userId, {
        stat: 0,
        lastssen: new Date()
      }).catch(console.error);
    }
  }

  async handleJoinRoom(socket, data) {
    const { roomId } = data;

    if (!roomId) {
      throw new Error('معرف الغرفة مطلوب');
    }

    // Check if room exists
    const room = await Room.findOne({ id: roomId });
    if (!room) {
      throw new Error('الغرفة غير موجودة');
    }

    // Join the room
    await this.joinRoom(socket, roomId);

    // Get recent messages
    const messages = await Message.getRecentMessages(roomId, 50);
    socket.emit('message_history', messages);
  }

  async handleLeaveRoom(socket, data) {
    const { roomId } = data;

    if (socket.currentRoom === roomId) {
      socket.leave(roomId);
      this.removeUserFromRoom(roomId, socket.userId);
      socket.currentRoom = null;

      socket.emit('room_left', { roomId });
    }
  }

  async handleSendMessage(socket, data) {
    const { msg, roomId, type = 'text' } = data;

    if (!socket.user) {
      throw new Error('يجب تسجيل الدخول أولاً');
    }

    if (!msg || msg.trim().length === 0) {
      throw new Error('الرسالة فارغة');
    }

    if (msg.length > 250) {
      throw new Error('الرسالة طويلة جداً');
    }

    if (!roomId || socket.currentRoom !== roomId) {
      throw new Error('يجب الانضمام للغرفة أولاً');
    }

    // Create message
    const message = await Message.create({
      msg: msg.trim(),
      roomId,
      userId: socket.userId,
      user: {
        id: socket.userId,
        topic: socket.user.topic,
        topic1: socket.user.topic1 || socket.user.topic,
        ucol: socket.user.ucol || '#000000',
        mcol: socket.user.mcol || '#000000',
        pic: socket.user.pic || 'pic.png',
        rep: socket.user.rep || 0
      },
      type,
      time: new Date()
    });

    // Broadcast to room
    this.io.to(roomId).emit('new_message', message);
  }

  async handleLikeMessage(socket, data) {
    const { messageId } = data;

    if (!socket.user) {
      throw new Error('يجب تسجيل الدخول أولاً');
    }

    const message = await Message.findById(messageId);
    if (!message) {
      throw new Error('الرسالة غير موجودة');
    }

    // Toggle like
    const userLiked = message.likes.includes(socket.userId);
    if (userLiked) {
      message.likes.pull(socket.userId);
    } else {
      message.likes.push(socket.userId);
    }

    await message.save();

    // Broadcast update
    this.io.to(message.roomId).emit('message_liked', {
      messageId,
      likes: message.likes.length,
      userLiked: !userLiked
    });
  }

  async handleDeleteMessage(socket, data) {
    const { messageId } = data;

    if (!socket.user) {
      throw new Error('يجب تسجيل الدخول أولاً');
    }

    const message = await Message.findById(messageId);
    if (!message) {
      throw new Error('الرسالة غير موجودة');
    }

    // Check if user can delete (own message or admin)
    if (message.userId !== socket.userId && !socket.user.isAdmin) {
      throw new Error('غير مسموح بحذف هذه الرسالة');
    }

    await Message.findByIdAndDelete(messageId);

    // Broadcast deletion
    this.io.to(message.roomId).emit('message_deleted', { messageId });
  }

  async handleStatusUpdate(socket, data) {
    const { status, nameColor, messageColor } = data;

    if (!socket.user) {
      throw new Error('يجب تسجيل الدخول أولاً');
    }

    // Update user
    const updates = {};
    if (status !== undefined) updates.msg = status;
    if (nameColor) updates.ucol = nameColor;
    if (messageColor) updates.mcol = messageColor;

    if (Object.keys(updates).length > 0) {
      await User.findByIdAndUpdate(socket.userId, updates);
      Object.assign(socket.user, updates);

      // Broadcast update to current room
      if (socket.currentRoom) {
        const onlineUsers = await this.getOnlineUsers(socket.currentRoom);
        this.io.to(socket.currentRoom).emit('online_users', onlineUsers);
      }
    }
  }

  handleLogout(socket) {
    if (socket.currentRoom) {
      this.removeUserFromRoom(socket.currentRoom, socket.userId);
    }

    socket.user = null;
    socket.userId = null;
    socket.disconnect();
  }

  async logSecurityEvent(socket, event, details = {}) {
    try {
      await Log.createSecurityLog({
        state: event,
        ip: socket.ip,
        fingerprint: socket.fingerprint,
        userAgent: socket.handshake.headers['user-agent'],
        userId: socket.userId,
        details,
        severity: 'medium'
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  // Get statistics
  getStats() {
    return {
      connectedUsers: this.connectedUsers.size,
      totalRooms: this.roomUsers.size,
      userSockets: this.userSockets.size
    };
  }
}

module.exports = SocketManager;
