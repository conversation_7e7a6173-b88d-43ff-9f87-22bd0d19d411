<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دردشة عربية حديثة</title>
    
    <!-- CSS Libraries -->
    <link rel="stylesheet" href="bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        
        .main-container {
            display: flex;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-left: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
        }
        
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }
        
        .header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .login-container {
            padding: 20px;
            flex: 1;
        }
        
        .nav-tabs {
            margin-bottom: 15px;
        }
        
        .nav-tabs > li > a {
            border-radius: 0;
            font-size: 12px;
            padding: 8px 12px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-control {
            border-radius: 4px;
            border: 1px solid #ddd;
            padding: 8px 12px;
            font-size: 14px;
        }
        
        .btn {
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
        }
        
        .online-users {
            background: #e9ecef;
            padding: 10px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            font-size: 12px;
        }
        
        .chat-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .message-user {
            font-weight: bold;
        }
        
        .message-time {
            color: #666;
        }
        
        .message-content {
            line-height: 1.4;
        }
        
        .chat-input {
            background: white;
            padding: 15px;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
        }
        
        .chat-input textarea {
            flex: 1;
            resize: none;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 14px;
        }
        
        .chat-input button {
            border-radius: 50%;
            width: 40px;
            height: 40px;
            padding: 0;
        }
        
        .hidden {
            display: none !important;
        }
        
        .connection-status {
            font-size: 11px;
            padding: 2px 8px;
            border-radius: 10px;
            background: #28a745;
            color: white;
        }
        
        .connection-status.danger {
            background: #dc3545;
        }
        
        .user-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            margin-top: 10px;
        }
        
        .user-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            overflow: hidden;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: bold;
            font-size: 13px;
        }
        
        .user-status {
            font-size: 11px;
            color: #666;
        }
        
        .toast {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px;
            z-index: 1000;
            min-width: 250px;
            animation: slideIn 0.3s ease;
        }
        
        .toast.error {
            border-left: 4px solid #dc3545;
        }
        
        .toast.success {
            border-left: 4px solid #28a745;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: 100vh;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .chat-area {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="header">
                <i class="fa fa-comments"></i> دردشة عربية حديثة
                <div class="connection-status" id="connectionStatus">يتم الاتصال...</div>
            </div>
            
            <!-- Login Container -->
            <div id="loginContainer" class="login-container">
                <!-- Login Tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active">
                        <a href="#guest" aria-controls="guest" role="tab" data-toggle="tab">
                            <i class="fa fa-user"></i> دخول الزوار
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#member" aria-controls="member" role="tab" data-toggle="tab">
                            <i class="fa fa-sign-in"></i> دخول الأعضاء
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#register" aria-controls="register" role="tab" data-toggle="tab">
                            <i class="fa fa-user-plus"></i> تسجيل عضوية
                        </a>
                    </li>
                </ul>
                
                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Guest Login -->
                    <div role="tabpanel" class="tab-pane active" id="guest">
                        <div class="form-group">
                            <input type="text" class="form-control" id="guestUsername" placeholder="أكتب الاسم المستعار" maxlength="50">
                        </div>
                        <button type="button" class="btn btn-primary btn-block" id="guestLoginBtn">
                            <i class="fa fa-sign-in"></i> دخول
                        </button>
                    </div>
                    
                    <!-- Member Login -->
                    <div role="tabpanel" class="tab-pane" id="member">
                        <div class="form-group">
                            <input type="text" class="form-control" id="username" placeholder="اكتب اسم العضو">
                        </div>
                        <div class="form-group">
                            <input type="password" class="form-control" id="password" placeholder="اكتب كلمة المرور">
                        </div>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" id="stealth"> دخول مخفي
                            </label>
                        </div>
                        <button type="button" class="btn btn-primary btn-block" id="userLoginBtn">
                            <i class="fa fa-sign-in"></i> دخول
                        </button>
                    </div>
                    
                    <!-- Registration -->
                    <div role="tabpanel" class="tab-pane" id="register">
                        <div class="form-group">
                            <input type="text" class="form-control" id="regUsername" placeholder="اكتب اسم العضو" maxlength="50">
                        </div>
                        <div class="form-group">
                            <input type="password" class="form-control" id="regPassword" placeholder="اكتب كلمة المرور" minlength="6">
                        </div>
                        <button type="button" class="btn btn-success btn-block" id="registerBtn">
                            <i class="fa fa-user-plus"></i> تسجيل
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Online Users -->
            <div class="online-users">
                <i class="fa fa-users"></i> المتواجدين الآن: <span id="onlineCount">0</span>
                <div class="user-list hidden" id="usersList"></div>
            </div>
        </div>

        <!-- Chat Area -->
        <div class="chat-area hidden" id="chatContainer">
            <!-- Chat Header -->
            <div class="chat-header">
                <div>
                    <i class="fa fa-comments"></i> الغرفة العامة 1
                </div>
                <div>
                    <button class="btn btn-sm btn-default" id="usersToggle">
                        <i class="fa fa-users"></i> المتصلون (<span id="userCount">0</span>)
                    </button>
                    <button class="btn btn-sm btn-danger" id="logoutBtn">
                        <i class="fa fa-sign-out"></i> خروج
                    </button>
                </div>
            </div>

            <!-- Messages Area -->
            <div class="chat-messages" id="messagesContainer">
                <!-- Messages will be loaded here -->
            </div>

            <!-- Chat Input -->
            <div class="chat-input">
                <button class="btn btn-default" id="fileBtn" title="إرفاق ملف">
                    <i class="fa fa-paperclip"></i>
                </button>
                <textarea id="messageInput" placeholder="اكتب رسالتك هنا..." rows="1"></textarea>
                <button class="btn btn-primary" id="sendBtn">
                    <i class="fa fa-send"></i>
                </button>
            </div>

            <!-- Hidden file input -->
            <input type="file" id="fileInput" class="hidden" accept="image/*,audio/*,.pdf,.txt">
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <!-- JavaScript Libraries -->
    <script src="jquery-1.11.1.min.js"></script>
    <script src="bootstrap.min.js"></script>
    <script src="/socket.io/socket.io.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/modern-chat.js"></script>
</body>
</html>
