{"name": "modern-chat-app", "version": "2.0.0", "private": true, "description": "Modern Arabic Chat Application with Real-time Features", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["chat", "realtime", "arabic", "socket.io", "mongodb"], "author": "mahdislama", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "modern-chat-app": "file:", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "sanitize-html": "^2.11.0", "sharp": "^0.33.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "validator": "^13.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/node": "^20.10.5", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}