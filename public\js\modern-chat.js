/**
 * Modern Chat Application - Frontend JavaScript
 * Enhanced version with improved security and performance
 */

class ModernChat {
  constructor() {
    this.socket = null;
    this.user = null;
    this.currentRoom = null;
    this.token = localStorage.getItem('chatToken');
    this.fingerprint = null;
    this.isConnected = false;
    this.messageQueue = [];
    this.onlineUsers = new Map();
    
    this.init();
  }

  async init() {
    try {
      // Generate device fingerprint
      await this.generateFingerprint();
      
      // Setup UI event listeners
      this.setupEventListeners();
      
      // Initialize socket connection
      this.initSocket();
      
      // Load saved user data
      this.loadUserData();
      
      console.log('Modern Chat initialized successfully');
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      this.showError('فشل في تهيئة التطبيق');
    }
  }

  async generateFingerprint() {
    try {
      // Collect client data for fingerprinting
      const clientData = {
        screen: {
          width: screen.width,
          height: screen.height,
          colorDepth: screen.colorDepth
        },
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language,
        platform: navigator.platform,
        userAgent: navigator.userAgent,
        cookieEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack,
        hardwareConcurrency: navigator.hardwareConcurrency,
        maxTouchPoints: navigator.maxTouchPoints
      };

      // Send to server for secure fingerprint generation
      const response = await fetch('/api/fingerprint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ clientData })
      });

      const result = await response.json();
      if (result.success) {
        this.fingerprint = result.fingerprint;
        console.log('Device fingerprint generated:', this.fingerprint);
      } else {
        throw new Error('Failed to generate fingerprint');
      }
    } catch (error) {
      console.error('Fingerprint generation error:', error);
      // Fallback to simple fingerprint
      this.fingerprint = this.generateSimpleFingerprint();
    }
  }

  generateSimpleFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Fingerprint test', 2, 2);
    
    const canvasData = canvas.toDataURL();
    const screenData = `${screen.width}x${screen.height}x${screen.colorDepth}`;
    const combined = canvasData + screenData + navigator.userAgent;
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(16).padStart(8, '0') + '.simple.fp.generated';
  }

  initSocket() {
    try {
      this.socket = io({
        auth: {
          token: this.token,
          fingerprint: this.fingerprint
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5
      });

      this.setupSocketEvents();
    } catch (error) {
      console.error('Socket initialization error:', error);
      this.showError('فشل في الاتصال بالخادم');
    }
  }

  setupSocketEvents() {
    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.isConnected = true;
      this.updateConnectionStatus('متصل', 'success');
      this.processMessageQueue();
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
      this.isConnected = false;
      this.updateConnectionStatus('غير متصل', 'danger');
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.updateConnectionStatus('خطأ في الاتصال', 'danger');
      this.showError('فشل في الاتصال بالخادم');
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
      this.showError(error.message || 'حدث خطأ في الاتصال');
    });

    // Chat events
    this.socket.on('login_success', (data) => {
      this.handleLoginSuccess(data);
    });

    this.socket.on('room_joined', (data) => {
      this.handleRoomJoined(data);
    });

    this.socket.on('user_joined', (data) => {
      this.handleUserJoined(data);
    });

    this.socket.on('user_left', (data) => {
      this.handleUserLeft(data);
    });

    this.socket.on('new_message', (data) => {
      this.handleNewMessage(data);
    });

    this.socket.on('online_users', (users) => {
      this.updateOnlineUsers(users);
    });

    this.socket.on('message_history', (messages) => {
      this.loadMessageHistory(messages);
    });
  }

  setupEventListeners() {
    // Login forms
    document.getElementById('guestLoginBtn')?.addEventListener('click', () => {
      this.handleGuestLogin();
    });

    document.getElementById('userLoginBtn')?.addEventListener('click', () => {
      this.handleUserLogin();
    });

    document.getElementById('registerBtn')?.addEventListener('click', () => {
      this.handleUserRegistration();
    });

    // Message sending
    document.getElementById('sendBtn')?.addEventListener('click', () => {
      this.sendMessage();
    });

    document.getElementById('messageInput')?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    // File upload
    document.getElementById('fileInput')?.addEventListener('change', (e) => {
      this.handleFileUpload(e.target.files[0]);
    });

    // Room switching
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('room-btn')) {
        const roomId = e.target.dataset.roomId;
        this.joinRoom(roomId);
      }
    });

    // Logout
    document.getElementById('logoutBtn')?.addEventListener('click', () => {
      this.logout();
    });
  }

  async handleGuestLogin() {
    const username = document.getElementById('guestUsername')?.value?.trim();
    
    if (!username) {
      this.showError('يرجى إدخال اسم المستخدم');
      return;
    }

    if (username.length < 3 || username.length > 50) {
      this.showError('اسم المستخدم يجب أن يكون بين 3 و 50 حرف');
      return;
    }

    try {
      this.socket.emit('guest_login', {
        username,
        clientData: await this.getClientData()
      });
    } catch (error) {
      console.error('Guest login error:', error);
      this.showError('فشل في تسجيل دخول الزائر');
    }
  }

  async handleUserLogin() {
    const username = document.getElementById('username')?.value?.trim();
    const password = document.getElementById('password')?.value;
    const stealth = document.getElementById('stealth')?.checked;

    if (!username || !password) {
      this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username,
          password,
          stealth,
          fingerprint: this.fingerprint
        })
      });

      const result = await response.json();
      
      if (result.success) {
        this.token = result.token;
        localStorage.setItem('chatToken', this.token);
        this.handleLoginSuccess(result);
      } else {
        this.showError(result.message || 'فشل في تسجيل الدخول');
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showError('فشل في تسجيل الدخول');
    }
  }

  async handleUserRegistration() {
    const username = document.getElementById('regUsername')?.value?.trim();
    const password = document.getElementById('regPassword')?.value;

    if (!username || !password) {
      this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    if (password.length < 6) {
      this.showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    try {
      const response = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username,
          password,
          fingerprint: this.fingerprint,
          clientData: await this.getClientData()
        })
      });

      const result = await response.json();
      
      if (result.success) {
        this.token = result.token;
        localStorage.setItem('chatToken', this.token);
        this.handleLoginSuccess(result);
        this.showSuccess('تم التسجيل بنجاح');
      } else {
        this.showError(result.message || 'فشل في التسجيل');
      }
    } catch (error) {
      console.error('Registration error:', error);
      this.showError('فشل في التسجيل');
    }
  }

  handleLoginSuccess(data) {
    this.user = data.user;
    this.hideLoginForms();
    this.showChatInterface();
    this.updateUserInfo();
    
    // Join default room if not already in one
    if (!this.currentRoom) {
      this.joinRoom(this.user.roomid || 'efOiAhhNdL');
    }
  }

  joinRoom(roomId) {
    if (this.isConnected && roomId) {
      this.socket.emit('join_room', { roomId });
      this.currentRoom = roomId;
    }
  }

  sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input?.value?.trim();

    if (!message || !this.isConnected || !this.currentRoom) {
      return;
    }

    if (message.length > 250) {
      this.showError('الرسالة طويلة جداً (الحد الأقصى 250 حرف)');
      return;
    }

    const messageData = {
      msg: message,
      roomId: this.currentRoom,
      type: 'text'
    };

    if (this.isConnected) {
      this.socket.emit('send_message', messageData);
      input.value = '';
    } else {
      this.messageQueue.push(messageData);
      this.showError('سيتم إرسال الرسالة عند الاتصال');
    }
  }

  async getClientData() {
    return {
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      platform: navigator.platform
    };
  }

  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift();
      this.socket.emit('send_message', message);
    }
  }

  updateConnectionStatus(status, type) {
    const statusElement = document.querySelector('.connection-status');
    if (statusElement) {
      statusElement.textContent = status;
      statusElement.className = `connection-status ${type}`;
    }
  }

  showError(message) {
    this.showToast(message, 'error');
    console.error(message);
  }

  showSuccess(message) {
    this.showToast(message, 'success');
    console.log(message);
  }

  showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
      <div class="toast-content">
        <i class="fas ${this.getToastIcon(type)}"></i>
        <span>${message}</span>
      </div>
    `;

    toastContainer.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 5000);

    // Allow manual close
    toast.addEventListener('click', () => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    });
  }

  getToastIcon(type) {
    const icons = {
      success: 'fa-check-circle',
      error: 'fa-exclamation-circle',
      warning: 'fa-exclamation-triangle',
      info: 'fa-info-circle'
    };
    return icons[type] || icons.info;
  }

  hideLoginForms() {
    document.getElementById('loginContainer')?.classList.add('hidden');
  }

  showChatInterface() {
    document.getElementById('chatContainer')?.classList.remove('hidden');

    // Show users list toggle functionality
    document.getElementById('usersToggle')?.addEventListener('click', () => {
      const usersList = document.getElementById('usersList');
      if (usersList) {
        usersList.classList.toggle('hidden');
      }
    });
  }

  updateUserInfo() {
    if (this.user) {
      const userNameElement = document.querySelector('.user-name');
      if (userNameElement) {
        userNameElement.textContent = this.user.topic1 || this.user.topic;
      }
    }
  }

  loadUserData() {
    const savedUser = localStorage.getItem('chatUser');
    if (savedUser) {
      try {
        this.user = JSON.parse(savedUser);
      } catch (error) {
        console.error('Failed to load saved user data:', error);
        localStorage.removeItem('chatUser');
      }
    }
  }

  logout() {
    this.socket?.emit('logout');
    this.socket?.disconnect();
    
    // Clear stored data
    localStorage.removeItem('chatToken');
    localStorage.removeItem('chatUser');
    
    // Reset state
    this.user = null;
    this.token = null;
    this.currentRoom = null;
    this.isConnected = false;
    
    // Show login forms
    document.getElementById('loginContainer')?.classList.remove('hidden');
    document.getElementById('chatContainer')?.classList.add('hidden');
  }

  // Additional methods for handling other events...
  handleRoomJoined(data) {
    console.log('Joined room:', data);
    this.updateOnlineUsers(data.onlineUsers);
  }

  handleUserJoined(data) {
    console.log('User joined:', data);
    // Add join message to chat
  }

  handleUserLeft(data) {
    console.log('User left:', data);
    // Add leave message to chat
  }

  handleNewMessage(data) {
    console.log('New message:', data);
    this.addMessageToChat(data);
  }

  addMessageToChat(messageData) {
    const messagesContainer = document.getElementById('messagesContainer');
    if (!messagesContainer) return;

    const messageElement = document.createElement('div');
    messageElement.className = 'message';

    const isOwnMessage = messageData.user && messageData.user.id === this.user?.id;
    if (isOwnMessage) {
      messageElement.classList.add('own-message');
    }

    const timestamp = new Date(messageData.time || Date.now()).toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    });

    messageElement.innerHTML = `
      <div class="message-header">
        <img src="${messageData.user?.pic || 'pic.png'}" class="message-avatar" onerror="this.src='pic.png'">
        <span class="message-user" style="color: ${messageData.user?.ucol || messageData.user?.nameColor || '#000'}">${messageData.user?.topic || messageData.user?.topic1 || 'مجهول'}</span>
        <span class="message-time">${timestamp}</span>
      </div>
      <div class="message-content" style="color: ${messageData.user?.mcol || messageData.user?.messageColor || '#000'}">${this.escapeHtml(messageData.msg)}</div>
    `;

    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  loadMessageHistory(messages) {
    const messagesContainer = document.getElementById('messagesContainer');
    if (!messagesContainer) return;

    messagesContainer.innerHTML = '';
    messages.forEach(message => {
      this.addMessageToChat(message);
    });
  }

  updateOnlineUsers(users) {
    this.onlineUsers.clear();
    users.forEach(user => {
      this.onlineUsers.set(user.id, user);
    });

    // Update online users count
    const countElements = document.querySelectorAll('.online-count, #onlineCount, #usersBadge');
    countElements.forEach(element => {
      if (element) {
        element.textContent = users.length;
      }
    });

    // Update users list
    this.updateUsersList(users);
  }

  updateUsersList(users) {
    const usersList = document.getElementById('usersList');
    if (!usersList) return;

    usersList.innerHTML = '';
    users.forEach(user => {
      const userElement = document.createElement('div');
      userElement.className = 'user-item';
      userElement.innerHTML = `
        <div class="user-avatar">
          <img src="${user.pic || 'pic.png'}" alt="${user.topic}" onerror="this.src='pic.png'">
        </div>
        <div class="user-info">
          <div class="user-name" style="color: ${user.nameColor || '#000'}">${user.topic || 'مجهول'}</div>
          <div class="user-status">${user.msg || 'متاح'}</div>
        </div>
        <div class="user-country">
          <img src="flag/${user.co || 'iq'}.png" alt="${user.co}" onerror="this.src='flag/iq.png'">
        </div>
      `;
      usersList.appendChild(userElement);
    });
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  loadMessageHistory(messages) {
    console.log('Message history loaded:', messages);
    // Populate chat with message history
  }

  async handleFileUpload(file) {
    if (!file) return;

    const formData = new FormData();
    formData.append('photo', file);

    try {
      const response = await fetch('/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        // Send file message
        this.socket.emit('send_message', {
          msg: file.name,
          roomId: this.currentRoom,
          type: 'file',
          attachment: {
            url: result.url,
            filename: result.filename,
            originalName: result.originalName,
            size: result.size,
            mimetype: result.mimetype
          }
        });
      } else {
        this.showError(result.message || 'فشل في رفع الملف');
      }
    } catch (error) {
      console.error('File upload error:', error);
      this.showError('فشل في رفع الملف');
    }
  }
}

// Initialize the chat application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.modernChat = new ModernChat();
});
