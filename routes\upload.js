const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Allowed file types
const ALLOWED_TYPES = {
  images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  audio: ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'],
  video: ['video/mp4', 'video/webm', 'video/avi', 'video/mov'],
  documents: ['application/pdf', 'text/plain']
};

const ALL_ALLOWED_TYPES = [
  ...ALLOWED_TYPES.images,
  ...ALLOWED_TYPES.audio,
  ...ALLOWED_TYPES.video,
  ...ALLOWED_TYPES.documents
];

// File size limits (in bytes)
const SIZE_LIMITS = {
  images: 5 * 1024 * 1024, // 5MB
  audio: 10 * 1024 * 1024, // 10MB
  video: 50 * 1024 * 1024, // 50MB
  documents: 2 * 1024 * 1024, // 2MB
  default: 6 * 1024 * 1024 // 6MB
};

// Create upload directories if they don't exist
const createUploadDirs = async () => {
  const dirs = ['uploads', 'uploads/images', 'uploads/audio', 'uploads/video', 'uploads/documents', 'uploads/sico', 'uploads/emo', 'uploads/dro3'];
  
  for (const dir of dirs) {
    try {
      await fs.access(dir);
    } catch {
      await fs.mkdir(dir, { recursive: true });
    }
  }
};

// Initialize upload directories
createUploadDirs().catch(console.error);

// Configure multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads';
    
    // Determine upload path based on file type or query parameter
    if (req.query.fo) {
      uploadPath = path.join('uploads', req.query.fo);
    } else if (ALLOWED_TYPES.images.includes(file.mimetype)) {
      uploadPath = 'uploads/images';
    } else if (ALLOWED_TYPES.audio.includes(file.mimetype)) {
      uploadPath = 'uploads/audio';
    } else if (ALLOWED_TYPES.video.includes(file.mimetype)) {
      uploadPath = 'uploads/video';
    } else if (ALLOWED_TYPES.documents.includes(file.mimetype)) {
      uploadPath = 'uploads/documents';
    }
    
    cb(null, uploadPath);
  },
  
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueId = uuidv4();
    const timestamp = Date.now();
    const ext = path.extname(file.originalname).toLowerCase();
    const baseName = path.basename(file.originalname, ext)
      .replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')
      .substring(0, 20);
    
    const filename = `${baseName}_${uniqueId}_${timestamp}${ext}`;
    cb(null, filename);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Check if file type is allowed
  if (!ALL_ALLOWED_TYPES.includes(file.mimetype)) {
    return cb(new Error(`نوع الملف غير مدعوم: ${file.mimetype}`), false);
  }
  
  // Additional security checks
  const ext = path.extname(file.originalname).toLowerCase();
  const dangerousExts = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.js', '.jar'];
  
  if (dangerousExts.includes(ext)) {
    return cb(new Error('نوع الملف غير آمن'), false);
  }
  
  cb(null, true);
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: SIZE_LIMITS.default,
    files: 1 // Only one file at a time
  }
});

// Middleware to check file size based on type
const checkFileSize = (req, res, next) => {
  if (!req.file) {
    return next();
  }
  
  const file = req.file;
  let maxSize = SIZE_LIMITS.default;
  
  if (ALLOWED_TYPES.images.includes(file.mimetype)) {
    maxSize = SIZE_LIMITS.images;
  } else if (ALLOWED_TYPES.audio.includes(file.mimetype)) {
    maxSize = SIZE_LIMITS.audio;
  } else if (ALLOWED_TYPES.video.includes(file.mimetype)) {
    maxSize = SIZE_LIMITS.video;
  } else if (ALLOWED_TYPES.documents.includes(file.mimetype)) {
    maxSize = SIZE_LIMITS.documents;
  }
  
  if (file.size > maxSize) {
    // Delete the uploaded file
    fs.unlink(file.path).catch(console.error);
    return res.status(400).json({
      success: false,
      message: `حجم الملف كبير جداً. الحد الأقصى: ${Math.round(maxSize / 1024 / 1024)}MB`
    });
  }
  
  next();
};

// Image optimization middleware
const optimizeImage = async (req, res, next) => {
  if (!req.file || !ALLOWED_TYPES.images.includes(req.file.mimetype)) {
    return next();
  }
  
  try {
    const inputPath = req.file.path;
    const outputPath = inputPath.replace(/\.[^/.]+$/, '_optimized.webp');
    
    // Optimize image with sharp
    await sharp(inputPath)
      .resize(1200, 1200, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .webp({ quality: 80 })
      .toFile(outputPath);
    
    // Replace original file with optimized version
    await fs.unlink(inputPath);
    req.file.path = outputPath;
    req.file.filename = path.basename(outputPath);
    req.file.mimetype = 'image/webp';
    
  } catch (error) {
    console.error('Image optimization error:', error);
    // Continue without optimization if it fails
  }
  
  next();
};

// Main upload endpoint
router.post('/', upload.single('photo'), checkFileSize, optimizeImage, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم اختيار ملف'
      });
    }
    
    const file = req.file;
    const fileType = req.query.fo || 'general';
    
    // Generate file URL
    const fileUrl = `/uploads/${path.relative('uploads', file.path).replace(/\\/g, '/')}`;
    
    // Prepare response based on file type
    let responseData = {
      success: true,
      filename: file.filename,
      originalName: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      url: fileUrl,
      uploadedAt: new Date().toISOString()
    };
    
    // Legacy compatibility for specific upload types
    if (fileType === 'sico') {
      responseData.data = 'sico';
    } else if (fileType === 'dro3') {
      responseData.data = 'dro3';
    } else if (fileType === 'emo') {
      responseData.data = 'emo';
    }
    
    res.json(responseData);
    
  } catch (error) {
    console.error('Upload error:', error);
    
    // Clean up file if it exists
    if (req.file && req.file.path) {
      fs.unlink(req.file.path).catch(console.error);
    }
    
    res.status(500).json({
      success: false,
      message: 'خطأ في رفع الملف'
    });
  }
});

// Get file info endpoint
router.get('/info/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join('uploads', filename);
    
    // Check if file exists
    try {
      const stats = await fs.stat(filePath);
      
      res.json({
        success: true,
        filename,
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      });
      
    } catch (error) {
      res.status(404).json({
        success: false,
        message: 'الملف غير موجود'
      });
    }
    
  } catch (error) {
    console.error('File info error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب معلومات الملف'
    });
  }
});

// Delete file endpoint (for authorized users)
router.delete('/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    
    // Security check: only allow deletion of files in uploads directory
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'اسم ملف غير صحيح'
      });
    }
    
    const filePath = path.join('uploads', filename);
    
    try {
      await fs.unlink(filePath);
      
      res.json({
        success: true,
        message: 'تم حذف الملف بنجاح'
      });
      
    } catch (error) {
      res.status(404).json({
        success: false,
        message: 'الملف غير موجود'
      });
    }
    
  } catch (error) {
    console.error('File deletion error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الملف'
    });
  }
});

// Error handling middleware
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'حجم الملف كبير جداً'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'عدد الملفات كبير جداً'
      });
    }
  }
  
  res.status(400).json({
    success: false,
    message: error.message || 'خطأ في رفع الملف'
  });
});

module.exports = router;
